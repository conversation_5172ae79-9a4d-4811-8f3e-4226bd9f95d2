<template>
  <ClientOnly>
    <article class="flex flex-col sm:flex-row gap-4 items-center">
      <div v-intersect="{ threshold: 0 }" class="max-w-xs w-full shrink profile-foto">
        <!-- Mobile görünüm için <PERSON> resim (640px altında) -->
        <NuxtImg
          class="border-[5px] rounded-2xl object-cover w-full block sm:hidden"
          src="/images/profile_foto_mobile.webp"
          alt="<PERSON>"
          loading="lazy"
        />
        <!-- Desktop görünüm için büyük resim (640px ve üstünde) -->
        <NuxtImg
          class="border-[5px] rounded-2xl object-cover w-full hidden sm:block"
          src="/images/profile_foto.webp"
          alt="<PERSON>"
          loading="lazy"
        />
      </div>
      <div v-intersect class="grow flex flex-col p-2 gap-4 profile-desc">
        <div>
          <h2 class="text-2xl flex items-center">
            {{ t("home.hi") }}
            <Icon icon="line-md:emoji-smile-filled" class="size-8 ml-2"/>
          </h2>
          <h1 class="text-3xl">
            {{ t("home.title") }}
            <span class="">{{ t("home.name") }}</span>
          </h1>
          <h3 class="text-md text-green-600 dark:text-green-300">{{ t("home.degree") }}</h3>
        </div>
        <div>
          <p class="text-lg" v-for="(item,index) in t('home.description').split('\n')" :key="index">
            {{ item }}
          </p>
        </div>
        <ContactButtons hide-location-btn/>
      </div>
    </article>
  </ClientOnly>
  <ProjectsSlider class="mt-16"/>
</template>

<script setup lang="ts">
import {Icon} from '@iconify/vue';
import ProjectsSlider from "~/components/ProjectsSlider.vue";

const {t} = useI18n();

useHead({
  title: () => ("Abdullah Tunçer | " + t('menu.home'))
})
</script>

<style scoped>
@reference "tailwindcss";

.profile-foto {
  @apply -rotate-[75] -translate-y-20 scale-[25]
  opacity: 0;
  transform: rotateZ(-75deg) translateY(-260px) scale(0.25);
  transition: transform 1s ease-in-out, opacity 1s ease-in-out;
}

.profile-foto.show {
  opacity: 1;
  transform: rotateZ(0deg) translateY(0) scale(1);
}

/* Tailwind screen direktifi kullanımı */
@screen max-sm {
  .profile-foto {
    transform: rotateZ(-30deg) translateY(-100px) scale(0.5);
  }
}

.profile-desc{
  opacity: 0;
  transform: translateX(60px) scale(0.8);
  transition: transform 1s ease-in-out, opacity 1s ease-in-out;
}

.profile-desc.show{
  opacity: 1;
  transform: translateX(0) scale(1);
}
</style>