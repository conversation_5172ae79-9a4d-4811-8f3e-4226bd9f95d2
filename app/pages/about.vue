<template>
  <ClientOnly>
    <section class="flex flex-col gap-6 ">
      <h1 class="text-3xl font-bold">Hakkımda</h1>
      <p v-intersect v-html="marked.parseInline(t('aboutMe.intro'))" class="paragraf"/>
      <p v-intersect v-html="marked.parseInline(t('aboutMe.career'))" class="paragraf"/>
      <p v-intersect v-html="marked.parseInline(t('aboutMe.projects'))" class="paragraf"/>
      <p v-intersect v-html="marked.parseInline(t('aboutMe.goals'))" class="paragraf"/>
      <p v-intersect v-html="marked.parseInline(t('aboutMe.closing'))" class="paragraf"/>
      <article class="flex flex-col gap-3">
        <h2 class="text-2xl font-bold">{{ t("aboutMe.techs.title") }}</h2>
        <p v-intersect class="paragraf">{{ t("aboutMe.techs.description") }}</p>
        <div class="mt-3 grid sm:grid-cols-2 gap-4 items-start flex-wrap">
          <TechCard v-intersect title="Frontend" :techs="frontend_teknolojiler" class="tech-card left"/>
          <TechCard v-intersect title="State & Router" :techs="state_router_teknolojiler" class="tech-card right"/>
          <TechCard v-intersect title="Backend & Database" :techs="backend_database_teknolojiler"
                    class="tech-card left"/>
          <TechCard v-intersect :title="t('aboutMe.techs.other')" :techs="other_teknolojiler" class="tech-card right"/>
          <TechCard v-intersect :title="t('aboutMe.techs.tools')" :techs="tools" class="tech-card left"/>
          <TechCard v-intersect :title="t('aboutMe.techs.aiTools')" :techs="ai_tools" class="tech-card right"/>
        </div>
      </article>
      <article class="flex flex-col gap-3">
        <!-- Education & Experience -->
        <h2 class="text-2xl font-bold">{{ t("aboutMe.eduExp.title") }}</h2>
        <TimeLine :items="tm('aboutMe.eduExp.items')">
          <template #default="{dataItem}">
            <div class="border-none rounded border-current/20 px-4 py-2 flex flex-col gap-2">
              <h3 class="text-lg flex items-center gap-3 font-bold">
                <Icon v-if="dataItem.isEdu" icon="mdi:education-outline" class="size-8"/>
                <Icon v-else icon="mdi:company" class="size-8"/>
                {{ getLocalizedText(dataItem.title) }}
              </h3>
              <p class="text-sm font-bold">{{ getLocalizedText(dataItem.position) }} | {{ getLocalizedText(dataItem.time) }}</p>
              <p v-html="marked.parseInline(getLocalizedText(dataItem.description))"/>
              <ul class="list-disc list-inside">
                <li class="my-1" v-for="item in dataItem.list" v-html="marked.parseInline(getLocalizedText(item))"/>
              </ul>
            </div>
          </template>
        </TimeLine>
      </article>
    </section>
  </ClientOnly>
</template>

<script setup>
const {t, tm} = useI18n();
import {marked} from 'marked';
import {Icon} from "@iconify/vue";

useHead({
  title: () => ("Abdullah Tunçer | " + t('menu.aboutMe'))
})

// Helper function to safely get localized text
const getLocalizedText = (item) => {
  // In development, tm() wraps strings in { loc: { source: "text" } }
  // In production, this might be different or undefined
  if (item && typeof item === 'object' && item.loc && item.loc.source) {
    return item.loc.source;
  }
  // Fallback to the item itself if it's a string or the structure is different
  return item || '';
}

const frontend_teknolojiler = [
  {name: "Vue 2/3", icon: "material-icon-theme:vue", link: "https://vuejs.org/"},
  {name: "Nuxt 4", icon: "material-icon-theme:nuxt", link: "https://nuxt.com/"},
  {name: "React", icon: "material-icon-theme:react", link: "https://react.dev/"},
  {name: "Typescript", icon: "material-icon-theme:typescript", link: "https://www.typescriptlang.org/"},
  {name: "Vuetify", icon: "logos:vuetifyjs", link: "https://vuetifyjs.com/en/"},
  {name: "Tailwind", icon: "devicon:tailwindcss", link: "https://tailwindcss.com/"}
];

const state_router_teknolojiler = [
  {name: "Vue Router", icon: "material-icon-theme:vue", link: "https://router.vuejs.org/"},
  {name: "Vuex", icon: "material-icon-theme:vuex-store", link: "https://vuex.vuejs.org/"},
  {name: "Pinia", icon: "logos:pinia", link: "https://pinia.vuejs.org/"}
];

const backend_database_teknolojiler = [
  {name: "Express", icon: "simple-icons:express", link: "https://expressjs.com/"},
  {name: "MySQL", icon: "lineicons:mysql", link: "https://www.mysql.com/"},
  {name: "SQLite", icon: "devicon:sqlite", link: "https://sqlite.org/"}
];

const other_teknolojiler = [
  {name: "Electron", icon: "skill-icons:electron", link: "https://www.electronjs.org/"},
  {name: "Capacitor", icon: "material-icon-theme:capacitor", link: "https://capacitorjs.com/"},
  {name: "Socket.io", icon: "cib:socket-io", link: "https://socket.io/"}
];

const tools = [
  {name: "Webstorm", icon: "devicon:webstorm", link: "https://www.jetbrains.com/webstorm/"},
  {name: "Git", icon: "material-icon-theme:git", link: "https://git-scm.com/"},
  {name: "GitHub", icon: "lineicons:github", link: "https://github.com/"},
  {name: "Bitbucket", icon: "material-icon-theme:bitbucket", link: "https://bitbucket.org/"},
  {name: "Jira", icon: "logos:jira", link: "https://www.atlassian.com/software/jira"},
  {name: "Trello", icon: "logos:trello", link: "https://trello.com/"},
  {name: "Vite", icon: "devicon:vitejs", link: "https://vite.dev/"},
  {name: "Vitest", icon: "material-icon-theme:vitest", link: "https://vitest.dev/"},
  {name: "Docker", icon: "material-icon-theme:docker", link: "https://www.docker.com/"}
];

const ai_tools = [
  {name: "ChatGPT", icon: "arcticons:openai-chatgpt", link: "https://chatgpt.com/"},
  {name: "Augment Code", icon: "tabler:ai", link: "https://www.augmentcode.com/"}
];

</script>

<style>
.tech-card {
  opacity: 0;
  transition: transform 0.8s ease, opacity 0.8s ease;
}

.tech-card.right {
  transform: translateX(60px);
}

.tech-card.left {
  transform: translateX(-60px);
}

.tech-card.show {
  opacity: 1;
  transform: translateX(0);
}

.paragraf {
  opacity: 0;
  transform: translateY(-60px);
  transition: transform 0.8s ease, opacity 0.8s ease;
}

.paragraf.show {
  opacity: 1;
  transform: translateY(0);
}
</style>