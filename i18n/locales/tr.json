{"menu": {"home": "<PERSON><PERSON><PERSON>", "projects": "<PERSON><PERSON><PERSON>", "blog": "Blog", "aboutMe": "Hakkımda", "contact": "İletişim", "aria": {"theme": "Temayı Değiştir", "toggleMenu": "Menüyü Aç/Kapat"}}, "home": {"hi": "<PERSON><PERSON><PERSON><PERSON>!", "title": "<PERSON> ", "name": "<PERSON>", "degree": "Frontend Geliştirici - <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Hayata modern ve yaratıcı arayüzler katan bir Frontend geliştiriciyim. \nAmacım; kullanıcıların severek kullandığı, hızlı ve etkili web deneyimleri oluşturmak.", "mail": "E-Posta", "projects": "<PERSON><PERSON><PERSON>", "aria": {"portfolio": "Portfolyo İkonu", "todo": "<PERSON><PERSON>", "envantr": "EnvanTR İkonu", "pdfReader": "PDF Okuyucu İkonu", "zirveImzaAraci": "Zirve İmza Aracı İkonu", "eIcrapro": "e-İcraPro İkonu", "icraproweb": "İcraProWeb İkonu"}}, "aboutMe": {"intro": "<PERSON><PERSON><PERSON><PERSON>, ben **<PERSON>**. 2021 yılında Süleyman Demirel Üniversitesi **[<PERSON><PERSON><PERSON><PERSON><PERSON>](https://muhendislik.sdu.edu.tr/bilmuh)** bölümünden mezun oldum. Öğrencilik dönemimden itibaren yazılım geliştirmeye ilgi duydum ve özellikle **Frontend geliştirme** alanında kendimi geliştirmeye odaklandım.", "career": "Profesyonel iş <PERSON>ımda, <PERSON><PERSON><PERSON> **Vue.js ekosistemi** üzerinde çalıştım. Bu süreçte **icra hukuku** alanında faaliyet gösteren projelerde görev aldım ve büro yönetimi, icra takip sistemleri ile **[UYAP](https://www.uyap.gov.tr/)** entegrasyonlu otomasyon çözümleri geliştirdim. Bu projeler, yüksek güvenlik ve veri bütünlüğü gerektirdiğinden hem teknik becerilerimi hem de problem çözme yeteneğimi ileriye taşıdı. Ayrıca, **Electron** kullanarak masaüstü uygulamaları geliştirme konusunda da deneyim kazandım.", "projects": "Bireysel projelerimde ise farklı teknolojiler üzerinde kendimi geliştirmeye odaklandım. Örneğin, **Capacitor.js** kullanarak mobil envanter yönetim sistemi geliştirdim. Bu proje sayesinde hem **offline çalışma mantığı** hem de **mobil cihazlara özel kullanıcı deneyimi** tasarımı konularında tecrübe edindim. Ayrıca, **Nuxt 4** üzerinde projeler geliştirerek **[SSR](https://nuxt.com/docs/4.x/getting-started/introduction#server-side-rendering)** konusundaki eksikliklerimi kapatıyorum. Bunun yanında, modern frontend dünyasında önemli bir yer edinen **React ekosistemi** üzerine çalışarak bilgi birikimimi çeşitlendirmeyi hedefliyorum.", "goals": "<PERSON>u anda öncelikli hedeflerim arasında, **test yazımı** ve **test odaklı geliştirme** konusunda kendimi geliştirmek yer alıyor. Bu alanda yetersiz olduğumu düşündüğüm için <PERSON> **unit test**, **integration test** ve **end-to-end test** pratiklerini öğrenerek profesyonel yaklaşımımı daha sağlam bir temele oturtmayı amaçlıyorum.", "closing": "Teknik becerilerimin yanı sıra, **öğrenmeye ve yeni teknolojilere hızlı adapte olmaya** büyük önem veriyorum. **Kod kalitesi**, **ölçeklenebilirlik** ve **kullanıcı deneyimi** gibi konularda titiz bir yaklaşım sergiliyorum. İlerleyen dönemde hedefim, **Vue**, **React** ve **test odaklı geliştirme** alanlarında edindiğim tecrübeleri birleştirerek daha geniş çaplı projelerde yer almak ve yazılım sektöründe kalıcı bir değer üretmek.", "techs": {"title": "Tek<PERSON>lojiler", "description": "Aşağıdaki teknolojiler üzerinde deneyim sa<PERSON>biyim. Karmaşık durumlarda dokümantasyon ve referansları kullanarak hızlı çözüm üretebiliyorum.", "other": "<PERSON><PERSON><PERSON>", "tools": "Araçlar", "aiTools": "AI Araçlar"}, "eduExp": {"title": "Eğitim & Deneyim", "items": [{"isEdu": false, "title": "Zirve Bilgisayar Ltd. Şti.", "position": "Frontend Geliştirici", "time": "Ağu 2021 - Kas 2024", "description": "Zirve <PERSON>’da **hukuk ve icra otomasyonu alanında yazılım projeleri** geliştiren ekipte **Frontend Developer** olarak görev aldım. Teknik geliştirmelerin yanı sıra, **ekip çalışması, problem çözme ve kurumsal ortama uyum** gibi yönlerde de önemli deneyimler kazandım.\n\n**Öne Çıkan Katkılar ve Kazanımlar:**", "list": ["**Ekip Çalışması ve İletişim:** Backend geliştiriciler, ürün sahipleri ve diğer ekip üyeleriyle yakın çalışarak müşteri ihtiyaçlarına uygun çözümler geliştirdim.", "**<PERSON><PERSON><PERSON>:** Hem mevcut projelerde bakım ve iyileştirme görevlerinde hem de yeni ürün geliştirmelerinde aktif rol alarak farklı gereksinimlere hızlı şekilde adapte oldum.", "**S<PERSON><PERSON>ç <PERSON>lılık:** Versiyon kontrol, code review ve görev yönetim araçlarını kullanarak düzenli bir geliştirme sürecine katkıda bulundum.", "**Problem Çözme:** Mevcut hukuk otomasyonu sistemlerinde darboğazları tespit ederek pratik çözümler önerdim.", "**S<PERSON>rekli <PERSON>ğrenme:** Vue.js alanındaki yetkinliğimi güçlendirirken, Electron, Express ve Bitbucket Pipelines ile CI/CD süreçleri gibi farklı teknolojilerde de deneyim kazandım.", "**Sorumluluk Alma:** Yeni özelliklerin geliştirilmesinin yanı sıra uygulamaların kararlı çalışması ve sorunsuz yayına alınmasında da sorumluluk üstlendim."]}, {"isEdu": true, "title": "<PERSON><PERSON><PERSON><PERSON> Demirel <PERSON>niversitesi", "position": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "Eyl 2017 - Ağu 2021", "description": "Lisans eğitimim süresince zorunlu stajlarımı tamamladım. 2021 yılında Bilgisayar Mühendisliği Lisans derecesi ile mezun oldum", "list": ["**GPA:** 3.23 / 4"]}, {"isEdu": false, "title": "Zirve Bilgisayar Ltd. Şti.", "position": "Yaz<PERSON><PERSON>ım <PERSON>ajı", "time": "Tem 2021 - Ağu 2021", "description": "Yazılım stajım süresince Vue.js ve modern front-end teknolojileri üzerine odaklandım. Vue CLI, Vue Router, Vuetify ve component tabanlı mimariyi kullanarak dinamik ve responsive kullanıcı arayüzleri geliştirdim. REST API entegrasyonu, form işlemleri ve Git sürüm kontrol sistemini aktif olarak kullandım. Takım içinde kod review, merge işlemleri ve hata giderme süreçlerine dahil olarak profesyonel yazılım geliştirme pratiklerini deneyimledim.", "list": ["**Borçlu & Alacaklı Yönetim Sistemi:** Vue.js ve Vuetify ile kart tabanlı liste ve detay görünümleri geliştirdim.", "**Form Yönetimi & Validasyon:** <PERSON><PERSON><PERSON> formlar, input validation ve kullanıcı etkileşimleri üzerine çalıştım.", "**Responsive Tasarım:** Mobil ve tablet uyumlu arayüzler tasarladım ve grid sistemlerini kullandım.", "**API Entegrasyonu:** REST API ile veri çekme, gönderme ve state yönetimi yaptım.", "**Git & Bitbucket:** Branch yönetimi, merge, conflict çözümleme ve takım halinde kod paylaşımı deneyimledim.", "**Optimizasyon & Debugging:** Performans iyileştirmeleri ve hata giderme süreçlerinde rol aldım."]}]}}, "projects": {"personal": "<PERSON><PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "portfolio": {"name": "<PERSON><PERSON><PERSON><PERSON>folyo", "description": ""}, "todo": {"name": "Todo", "description": ""}, "envantr": {"name": "EnvanTR", "description": ""}, "pdfReader": {"name": "PDF Okuyucu", "description": ""}, "zirveImzaAraci": {"name": "Zirve İmza <PERSON>", "description": ""}, "eIcrapro": {"name": "e-İcraPro", "description": ""}, "icraproweb": {"name": "İcraProWeb", "description": ""}}, "contact": {"name": "İsim", "email": "E-Posta", "subject": "<PERSON><PERSON>", "message": "<PERSON><PERSON>", "location": "<PERSON><PERSON>", "rules": {"nameRequired": "<PERSON><PERSON> <PERSON><PERSON>.", "emailInvalid": "Geçerli bir e-posta adresi giriniz.", "messageRequired": "<PERSON><PERSON> <PERSON><PERSON>."}, "send": "<PERSON><PERSON><PERSON>", "sendSuccess": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>.", "sendFail": "<PERSON><PERSON> bir <PERSON>."}}